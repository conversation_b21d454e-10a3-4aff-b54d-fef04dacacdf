"""
Orders-related Telegram handlers (view purchased card details)
"""

from __future__ import annotations

import logging
import asyncio
from datetime import datetime, timezone, timedelta

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from middleware import attach_common_middlewares
from services.user_service import UserService
from services.card_service import CardService
from services.cart_service import CartService
from services.external_api_service import get_external_api_service
from config.settings import get_settings
from database.connection import get_collection
from utils.texts import DEMO_WATERMARK

logger = logging.getLogger(__name__)


class OrdersHandlers:
    def __init__(self):
        from services.external_api_service import ExternalAPIService

        self.user_service = UserService()
        # Note: CardService should be created per-user with proper API selection
        # For now, use explicit API v1 to avoid global setting interference
        self.card_service = CardService(external_api_service=ExternalAPIService(api_version="v1"))
        self.cart_service = CartService()
        self.external_api = get_external_api_service()
        self.purchases = get_collection("purchases")

    async def cb_view_purchased_card(self, callback: CallbackQuery) -> None:
        """Show details for a purchased card by ID"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = parts[2]

            # Fetch order info and show details (sanitized)
            order = await self._fetch_order_for_card(str(db_user.id), card_id)

            # Build details text
            if order:
                details_text = self._format_order_details(order)
            else:
                details_text = f"Could not load details for card #{card_id}."

            # 60s countdown expiry
            expiry = datetime.now(timezone.utc) + timedelta(seconds=60)
            expiry_ts = int(expiry.timestamp())

            # Enhanced header with card information
            card_name = "Unknown Card"
            if order and isinstance(order, dict):
                bank = order.get("bank", "")
                brand = order.get("brand", "")
                if bank and brand:
                    card_name = f"{bank} {brand}"
                elif bank:
                    card_name = bank
                elif brand:
                    card_name = brand

            header = f"💳 <b>{card_name}</b>\n🔍 <i>Card Details & Status</i>\n"
            timer_line = self._format_timer(expiry_ts)
            body = details_text

            # Try to resolve external order id for check API
            order_id_for_check = None
            try:
                if isinstance(order, dict) and isinstance(order.get("_id"), int):
                    order_id_for_check = int(order.get("_id"))
                else:
                    order_id_for_check = await self._resolve_external_order_id(card_id)
            except Exception:
                order_id_for_check = None

            # Enhanced keyboard layout with view details option
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔍 Check Card Status",
                            callback_data=f"orders:check:{order_id_for_check or ''}:{card_id}:{expiry_ts}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="📋 View Full Details",
                            callback_data=f"orders:view_card_details:{card_id}",
                        )
                    ],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="📋 Order History", callback_data="menu:history"),
                    ],
                    [
                        InlineKeyboardButton(text="🏠 Main Menu", callback_data="menu:main"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )

            msg = await callback.message.edit_text(
                f"{header}{timer_line}\n\n{body}\n" + DEMO_WATERMARK,
                reply_markup=kb,
            )

            # Start countdown updater
            asyncio.create_task(
                self._run_countdown(
                    chat_id=msg.chat.id,
                    message_id=msg.message_id,
                    card_id=str(card_id),
                    expiry_ts=expiry_ts,
                    body_text=body,
                    order_id=str(order_id_for_check or ''),
                )
            )

            await callback.answer()
        except Exception as e:
            logger.error(f"Error showing purchased card: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def cb_check_card(self, callback: CallbackQuery) -> None:
        """Handle Check Card button with enhanced UI and better error handling"""
        try:
            parts = (callback.data or "").split(":")
            # orders:check:<order_id_or_empty>:<card_id>:<expiry>
            if len(parts) not in (4, 5):
                await callback.answer("❌ Invalid request format", show_alert=True)
                return
            if len(parts) == 4:
                order_id_str = ""
                card_id = parts[2]
                expiry_ts = int(parts[3])
            else:
                order_id_str = parts[2]
                card_id = parts[3]
                expiry_ts = int(parts[4])

            # Check expiry
            now_ts = int(datetime.now(timezone.utc).timestamp())
            if now_ts >= expiry_ts:
                await self._disable_check_button(callback.message, card_id)
                await callback.answer("⛔ Check period expired", show_alert=True)
                return

            # Show loading state
            await callback.answer("🔍 Checking card status...", show_alert=False)

            # Resolve external order id
            order_id = int(order_id_str) if order_id_str.isdigit() else None
            if order_id is None:
                order_id = await self._resolve_external_order_id(card_id)

            if order_id is None:
                await callback.answer("❌ Could not resolve order ID", show_alert=True)
                return

            # Call external check API with enhanced error handling
            logger.info(f"Checking card {card_id} for order {order_id}")
            resp = await self.external_api.check_order(order_id, card_id=card_id)

            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                # Parse response data
                data = resp.data.get("data") or {}
                status = data.get("status") or resp.data.get("status") or "Unknown"

                # Enhanced status display with icons and detailed information
                status_icon = self._get_status_icon(status)
                status_message = self._format_enhanced_status_message(status, data)

                # Show detailed status in alert
                await callback.answer(
                    f"{status_icon} Card Check Complete\n\n{status_message}",
                    show_alert=True
                )

                # Also update the message with the check result
                try:
                    current_text = callback.message.text or ""
                    if "Card Details & Status" in current_text:
                        # Add check result to the message
                        check_result_line = f"\n🔍 <b>Last Check:</b> {status_icon} {status}"
                        updated_text = current_text.replace(
                            "\n" + DEMO_WATERMARK,
                            check_result_line + "\n" + DEMO_WATERMARK
                        )
                        await callback.message.edit_text(
                            updated_text,
                            reply_markup=callback.message.reply_markup
                        )
                except Exception as edit_error:
                    logger.warning(f"Could not update message with check result: {edit_error}")

                logger.info(f"Card check successful: {status}")
            else:
                # Enhanced error handling with more specific messages
                error_msg = getattr(resp, 'error', 'Unknown error')
                if "timeout" in error_msg.lower():
                    await callback.answer(
                        "⏱️ Check Timeout\n\nThe card verification is taking longer than expected. This may indicate:\n• High server load\n• Network issues\n• Card verification in progress\n\nPlease try again in a few moments.",
                        show_alert=True
                    )
                elif "not found" in error_msg.lower():
                    await callback.answer(
                        "🔍 Card Not Found\n\nThis card may not be available for checking because:\n• Order is too old\n• Card has been removed\n• Temporary system issue\n\nTry viewing full card details first.",
                        show_alert=True
                    )
                elif "rate limit" in error_msg.lower():
                    await callback.answer(
                        "⏳ Rate Limited\n\nToo many check requests. Please wait a moment before trying again.",
                        show_alert=True
                    )
                else:
                    await callback.answer(
                        f"❌ Check Failed\n\n{error_msg}\n\nTry viewing full card details or contact support if the issue persists.",
                        show_alert=True
                    )

                logger.warning(f"Card check failed for {card_id}: {error_msg}")

        except Exception as e:
            logger.error(f"Error checking card {card_id}: {e}")
            await callback.answer(
                "❌ System error\n\nAn unexpected error occurred. Please try again later.",
                show_alert=True
            )

    async def cb_view_card_details(self, callback: CallbackQuery) -> None:
        """Show detailed card information using the view_card API endpoint"""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            parts = (callback.data or "").split(":")
            if len(parts) < 3:
                await callback.answer("❌ Invalid request", show_alert=True)
                return

            card_id = parts[2]

            # Show loading state
            await callback.answer("🔍 Loading card details...", show_alert=False)

            # Resolve external order id
            order_id = await self._resolve_external_order_id(card_id)
            if order_id is None:
                await callback.answer("❌ Could not find order for this card", show_alert=True)
                return

            # Call view_card API
            logger.info(f"Fetching card details for order_id={order_id}, card_id={card_id}")
            resp = await self.external_api.view_card(order_id)

            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                # Parse response data
                card_data = resp.data.get("data", {})

                # Format card details with filtering
                details_text = self._format_card_details(card_data)

                # Enhanced header
                bank = card_data.get("bank", "Unknown Bank")
                brand = card_data.get("brand", "")
                card_name = f"{bank} {brand}".strip() if brand else bank

                header = f"💳 <b>{card_name}</b>\n📋 <i>Complete Card Details</i>\n"

                # Enhanced keyboard with check option
                kb = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔍 Check Card Status",
                                callback_data=f"orders:check:{order_id}:{card_id}:{int((datetime.now(timezone.utc) + timedelta(seconds=60)).timestamp())}",
                            )
                        ],
                        [
                            InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                            InlineKeyboardButton(text="📋 Order History", callback_data="menu:history"),
                        ],
                        [
                            InlineKeyboardButton(text="🏠 Main Menu", callback_data="menu:main"),
                            InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                        ],
                    ]
                )

                await callback.message.edit_text(
                    f"{header}\n{details_text}\n" + DEMO_WATERMARK,
                    reply_markup=kb,
                )

                logger.info(f"Card details displayed successfully for order_id={order_id}")
            else:
                # Enhanced error handling
                error_msg = getattr(resp, 'error', 'Unknown error')
                await callback.answer(
                    f"❌ Failed to load card details\n\n{error_msg}",
                    show_alert=True
                )
                logger.warning(f"View card details failed for order_id={order_id}: {error_msg}")

        except Exception as e:
            logger.error(f"Error viewing card details for card {card_id}: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def _fetch_order_for_card(self, user_id: str | None, card_id: str) -> dict | None:
        """Fetch latest order entry for the specified product/card id.

        Priority: local DB purchases (non-sensitive) -> external orders API (sanitized).
        """
        try:
            # 1) Try local DB purchases
            if user_id:
                doc = await self.purchases.find_one(
                    {"user_id": user_id, "$or": [{"metadata.card_id": card_id}, {"sku": f"card_{card_id}"}]},
                    sort=[("created_at", -1)],
                )
                if doc:
                    meta = doc.get("metadata", {}) or {}
                    safe = {
                        "_id": str(doc.get("_id")),
                        "product_id": meta.get("card_id") or card_id,
                        "price": doc.get("price"),
                        "status": doc.get("status"),
                        "bank": (meta.get("card_data", {}) or {}).get("bank"),
                        "brand": (meta.get("card_data", {}) or {}).get("brand"),
                        "level": (meta.get("card_data", {}) or {}).get("level"),
                        "type": (meta.get("card_data", {}) or {}).get("type"),
                        "country": (meta.get("card_data", {}) or {}).get("country"),
                        "state": (meta.get("card_data", {}) or {}).get("state"),
                        "city": (meta.get("card_data", {}) or {}).get("city"),
                        "zip": (meta.get("card_data", {}) or {}).get("zip"),
                        "createdAt": doc.get("created_at"),
                    }
                    return safe

            # 2) Fallback to external API
            resp = await self.external_api.list_orders(page=1, limit=10)
            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                for od in (resp.data.get("data") or []):
                    pid = od.get("product_id") or od.get("card_id") or od.get("id")
                    if pid is not None and str(pid) == str(card_id):
                        # Sanitize sensitive fields before showing
                        return {
                            k: v
                            for k, v in od.items()
                            if k
                            not in {
                                "cc",
                                "cvv",
                                "ssn",
                                "dl",
                                "ua",
                                "dob",
                                "expmonth",
                                "expyear",
                                "email",
                                "phone",
                                "address",
                            }
                        }
            return None
        except Exception as e:
            logger.warning(f"Failed to fetch order for card {card_id}: {e}")
            return None

    async def _resolve_external_order_id(self, card_id: str) -> int | None:
        """Find the external order _id for a given product/card id from recent orders."""
        try:
            resp = await self.external_api.list_orders(page=1, limit=10)
            if getattr(resp, "success", False) and isinstance(resp.data, dict):
                data = resp.data.get("data") or []
                for od in data:
                    pid = od.get("product_id") or od.get("card_id") or od.get("id")
                    if pid is not None and str(pid) == str(card_id):
                        oid = od.get("_id")
                        if isinstance(oid, int):
                            return oid
            return None
        except Exception:
            return None

    async def cb_orders_menu(self, callback: CallbackQuery) -> None:
        """Show user's recent orders (from local DB purchases)."""
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return

            cursor = self.purchases.find({"user_id": str(db_user.id)}).sort("created_at", -1)
            docs = await cursor.limit(10).to_list(10)
            if not docs:
                text = (
                    "📦 <b>Your Order History</b>\n"
                    "🔍 <i>Recent Purchases & Cards</i>\n\n"
                    "📋 <b>No orders found</b>\n"
                    "You haven't made any purchases yet.\n\n"
                    "💡 <i>Start browsing our catalog to find cards!</i>"
                ) + DEMO_WATERMARK
                kb = InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(text="🛒 Browse Catalog", callback_data="menu:browse")],
                        [InlineKeyboardButton(text="⬅️ Back to Menu", callback_data="menu:main")]
                    ]
                )
                await callback.message.edit_text(text, reply_markup=kb)
                await callback.answer()
                return

            # Enhanced header with better visual hierarchy
            lines = [
                "📦 <b>Your Order History</b>",
                "🔍 <i>Recent Purchases & Cards</i>",
                "",
                f"📊 <b>Total Orders:</b> {len(docs)}",
                ""
            ]

            buttons_rows: list[list[InlineKeyboardButton]] = []

            for i, d in enumerate(docs, 1):
                meta = d.get("metadata", {}) or {}
                cd = meta.get("card_data", {}) or {}
                cid = meta.get("card_id")
                if not cid and isinstance(d.get("sku"), str) and d["sku"].startswith("card_"):
                    try:
                        cid = int(d["sku"].split("_", 1)[1])
                    except Exception:
                        cid = None

                price = float(d.get("price", 0.0))
                status = d.get("status", "")
                bank = cd.get("bank", "Unknown Bank")
                brand = cd.get("brand", "")
                level = cd.get("level", "")
                created = d.get("created_at")

                # Enhanced status icon
                status_icon = "✅" if status.lower() in ["completed", "active", "valid"] else "⚠️" if status.lower() in ["pending", "processing"] else "❌"

                # Format card display
                card_name = f"{bank}"
                if brand and brand != bank:
                    card_name += f" {brand}"
                if level:
                    card_name += f" ({level})"

                # Enhanced order line with better visual structure
                lines.append(f"💳 <b>{i}. {card_name}</b>")
                lines.append(f"   {status_icon} <b>Status:</b> {status}")
                lines.append(f"   💰 <b>Price:</b> ${price:.2f}")
                if created:
                    formatted_date = self._format_date(created) if hasattr(self, '_format_date') else created
                    lines.append(f"   📅 <b>Date:</b> {formatted_date}")

                # Add separator line for better readability
                lines.append("   " + "─" * 25)
                lines.append("")

                if cid:
                    # Check if card has been viewed/checked to determine display
                    is_viewed = meta.get("is_viewed", False) or d.get("isviewed", False)
                    viewed_at = meta.get("viewed_at") or d.get("viewedAt")
                    check_date = meta.get("check_date") or d.get("check_Date")

                    # If card has been checked/viewed, show details inline instead of button
                    if is_viewed or viewed_at or check_date:
                        # Display card details inline
                        card_details = await self._get_card_details_for_display(cid, d, meta)
                        if card_details:
                            lines.append(f"   📋 <b>Card Details:</b>")
                            lines.extend([f"   {line}" for line in card_details.split('\n') if line.strip()])
                    else:
                        # Show view button only if card hasn't been checked
                        view_text = f"🔍 View {bank[:12]}..." if len(bank) > 12 else f"🔍 View {bank}"
                        buttons_rows.append(
                            [
                                InlineKeyboardButton(
                                    text=view_text,
                                    callback_data=f"orders:view_card:{cid}",
                                )
                            ]
                        )

            # Enhanced navigation buttons
            buttons_rows.extend([
                [
                    InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                    InlineKeyboardButton(text="🔄 Refresh", callback_data="menu:history"),
                ],
                [
                    InlineKeyboardButton(text="🏠 Main Menu", callback_data="menu:main"),
                    InlineKeyboardButton(text="⬅️ Back", callback_data="menu:main"),
                ]
            ])

            await callback.message.edit_text(
                "\n".join(lines) + "\n" + DEMO_WATERMARK,
                reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons_rows),
            )
            await callback.answer()
        except Exception as e:
            logger.error(f"Error showing orders: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)

    async def _get_card_details_for_display(self, card_id: str, purchase_data: dict, metadata: dict) -> str:
        """Get formatted card details for inline display when card has been checked"""
        try:
            # Try to fetch detailed card info from API
            order_data = await self._fetch_order_for_card("", card_id)
            if order_data:
                return self._format_card_details_compact(order_data)

            # Fallback to purchase data if API call fails
            return self._format_card_details_compact(purchase_data)
        except Exception as e:
            logger.error(f"Error getting card details for display: {e}")
            return "Details unavailable"

    def _format_card_details_compact(self, card_data: dict) -> str:
        """Format card details in a compact format for inline display"""
        try:
            lines = []

            # Helper function to filter out null/empty values
            def is_valid_value(value):
                return value is not None and value != "" and value != "null" and str(value).strip() != ""

            # Core card information
            bank = card_data.get("bank")
            brand = card_data.get("brand")
            card_type = card_data.get("type")
            level = card_data.get("level")
            base = card_data.get("base")

            # Location
            country = card_data.get("country")
            state = card_data.get("state")
            city = card_data.get("city")
            zip_code = card_data.get("zip")

            # Technical details (non-sensitive)
            bin_number = card_data.get("bin")
            expiry = card_data.get("expiry")
            code = card_data.get("code")
            seller_id = card_data.get("seller_id")

            # Status and dates
            status = card_data.get("status", "Unknown")
            viewed_at = card_data.get("viewedAt")
            check_date = card_data.get("check_Date")

            # Format compact display
            if is_valid_value(bank) and is_valid_value(brand):
                lines.append(f"🏦 {bank} {brand}")
            elif is_valid_value(bank):
                lines.append(f"🏦 {bank}")

            if is_valid_value(card_type) and is_valid_value(level):
                lines.append(f"💳 {card_type} {level}")
            elif is_valid_value(card_type):
                lines.append(f"💳 {card_type}")

            if is_valid_value(country):
                location_parts = [country]
                if is_valid_value(state):
                    location_parts.append(state)
                if is_valid_value(city):
                    location_parts.append(city)
                lines.append(f"🌍 {', '.join(location_parts)}")

            if is_valid_value(bin_number):
                lines.append(f"🔢 BIN: {bin_number}")

            if is_valid_value(expiry):
                lines.append(f"📅 Expires: {expiry}")

            if is_valid_value(code):
                lines.append(f"🔐 Code: {code}")

            if is_valid_value(base):
                lines.append(f"🔧 Base: {base}")

            if is_valid_value(seller_id) and seller_id != "Not Allowed":
                lines.append(f"👤 Seller: {seller_id}")

            # Status with timestamp
            status_line = f"📊 Status: {status}"
            if is_valid_value(viewed_at):
                status_line += f" (Viewed: {self._format_date(viewed_at)})"
            elif is_valid_value(check_date):
                status_line += f" (Checked: {self._format_date(check_date)})"
            lines.append(status_line)

            return "\n".join(lines) if lines else "No details available"

        except Exception as e:
            logger.error(f"Error formatting compact card details: {e}")
            return "Error formatting details"

    def _format_order_details(self, order: dict) -> str:
        """Format order details with enhanced UI and complete field display"""
        try:
            lines = []

            # Helper function to filter out null/empty values
            def is_valid_value(value):
                return value is not None and value != "" and value != "null" and str(value).strip() != ""

            # Extract all available information
            order_id = order.get("_id")
            user_id = order.get("user_id")
            product_id = order.get("product_id")
            status = order.get("status", "Unknown")
            price = order.get("price")

            # Card information
            bank = order.get("bank")
            brand = order.get("brand")
            card_type = order.get("type")
            level = order.get("level")
            base = order.get("base")

            # Technical card details (non-sensitive)
            bin_number = order.get("bin")
            expiry = order.get("expiry")
            code = order.get("code")
            seller_id = order.get("seller_id")

            # Location information
            country = order.get("country")
            state = order.get("state")
            city = order.get("city")
            zip_code = order.get("zip")
            address = order.get("address")

            # Cardholder information (basic, non-sensitive)
            name = order.get("name")

            # Dates and status info
            created_at = order.get("createdAt")
            start_date = order.get("start_Date")
            viewed_at = order.get("viewedAt")
            check_date = order.get("check_Date")
            refund_at = order.get("refundAt")
            is_viewed = order.get("isviewed")
            refundable = order.get("refundable")

            # Header section with status
            status_icon = self._get_status_icon(status)
            lines.append(f"📊 <b>Status:</b> {status_icon} {status}")
            lines.append("")

            # Card details section
            lines.append("💳 <b>Card Information</b>")
            if is_valid_value(bank):
                lines.append(f"   🏦 <b>Bank:</b> {bank}")
            if is_valid_value(brand):
                lines.append(f"   🏷️ <b>Brand:</b> {brand}")
            if is_valid_value(card_type):
                lines.append(f"   📋 <b>Type:</b> {card_type}")
            if is_valid_value(level):
                lines.append(f"   ⭐ <b>Level:</b> {level}")
            if is_valid_value(base):
                lines.append(f"   🔧 <b>Base:</b> {base}")
            if is_valid_value(bin_number):
                lines.append(f"   🔢 <b>BIN:</b> {bin_number}")
            if is_valid_value(expiry):
                lines.append(f"   📅 <b>Expiry:</b> {expiry}")
            if is_valid_value(code):
                lines.append(f"   🔐 <b>Code:</b> {code}")
            lines.append("")

            # Financial information
            lines.append("💰 <b>Financial Details</b>")
            if is_valid_value(price):
                try:
                    price_float = float(price)
                    lines.append(f"   💵 <b>Price:</b> ${price_float:.2f}")
                except (ValueError, TypeError):
                    lines.append(f"   💵 <b>Price:</b> {price}")

            if is_valid_value(refundable):
                refund_text = "Yes" if refundable else "No"
                lines.append(f"   🔄 <b>Refundable:</b> {refund_text}")
            lines.append("")

            # Location information (if available)
            location_fields = [country, state, city, zip_code, address]
            if any(is_valid_value(field) for field in location_fields):
                lines.append("🌍 <b>Location Details</b>")
                if is_valid_value(country):
                    lines.append(f"   🏳️ <b>Country:</b> {country}")
                if is_valid_value(state):
                    lines.append(f"   📍 <b>State:</b> {state}")
                if is_valid_value(city):
                    lines.append(f"   🏙️ <b>City:</b> {city}")
                if is_valid_value(zip_code):
                    lines.append(f"   📮 <b>ZIP:</b> {zip_code}")
                if is_valid_value(address):
                    lines.append(f"   🏠 <b>Address:</b> {address}")
                lines.append("")

            # Cardholder information (basic only)
            if is_valid_value(name):
                lines.append("👤 <b>Cardholder Information</b>")
                lines.append(f"   📝 <b>Name:</b> {name}")
                lines.append("")

            # Order tracking
            date_fields = [created_at, start_date, viewed_at, check_date, refund_at]
            if any(is_valid_value(field) for field in date_fields):
                lines.append("📅 <b>Order Timeline</b>")
                if is_valid_value(created_at):
                    lines.append(f"   📝 <b>Created:</b> {self._format_date(created_at)}")
                if is_valid_value(start_date):
                    lines.append(f"   🚀 <b>Started:</b> {self._format_date(start_date)}")
                if is_valid_value(viewed_at):
                    lines.append(f"   👁️ <b>Viewed:</b> {self._format_date(viewed_at)}")
                if is_valid_value(check_date):
                    lines.append(f"   🔍 <b>Checked:</b> {self._format_date(check_date)}")
                if is_valid_value(refund_at):
                    lines.append(f"   🔄 <b>Refunded:</b> {self._format_date(refund_at)}")
                lines.append("")

            # Status information
            if is_valid_value(is_viewed):
                view_status = "Yes" if is_viewed else "No"
                lines.append(f"👁️ <b>Viewed:</b> {view_status}")
                lines.append("")

            # Technical details
            lines.append("🔧 <b>Technical Info</b>")
            if is_valid_value(order_id):
                lines.append(f"   🆔 <b>Order ID:</b> {order_id}")
            if is_valid_value(user_id):
                lines.append(f"   👤 <b>User ID:</b> {user_id}")
            if is_valid_value(product_id):
                lines.append(f"   🎯 <b>Product ID:</b> {product_id}")
            if is_valid_value(seller_id) and seller_id != "Not Allowed":
                lines.append(f"   🏪 <b>Seller ID:</b> {seller_id}")

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Error formatting order details: {e}")
            return "❌ <b>Error loading card details</b>\n\nPlease try again or contact support if the issue persists."

    def _format_card_details(self, card_data: dict) -> str:
        """Format complete card details with enhanced UI and field filtering"""
        try:
            lines = []

            # Helper function to filter out null/empty values
            def is_valid_value(value):
                return value is not None and value != "" and value != "null" and str(value).strip() != ""

            # Extract key information
            order_id = card_data.get("_id")
            user_id = card_data.get("user_id")
            product_id = card_data.get("product_id")
            status = card_data.get("status", "Unknown")
            price = card_data.get("price")

            # Card information
            bank = card_data.get("bank")
            brand = card_data.get("brand")
            card_type = card_data.get("type")
            level = card_data.get("level")
            base = card_data.get("base")

            # Technical card details (non-sensitive)
            bin_number = card_data.get("bin")
            expiry = card_data.get("expiry")
            code = card_data.get("code")
            seller_id = card_data.get("seller_id")

            # Additional technical fields (if available and non-sensitive)
            track1 = card_data.get("track1")
            track2 = card_data.get("track2")
            pin = card_data.get("pin")

            # Location information
            country = card_data.get("country")
            state = card_data.get("state")
            city = card_data.get("city")
            zip_code = card_data.get("zip")
            address = card_data.get("address")

            # Cardholder information (filtered for security)
            name = card_data.get("name")
            email = card_data.get("email")
            phone = card_data.get("phone")

            # Dates
            created_at = card_data.get("createdAt")
            start_date = card_data.get("start_Date")
            viewed_at = card_data.get("viewedAt")
            check_date = card_data.get("check_Date")
            refund_at = card_data.get("refundAt")

            # Status section with icon
            status_icon = self._get_status_icon(status)
            lines.append(f"📊 <b>Status:</b> {status_icon} {status}")
            lines.append("")

            # Card details section
            lines.append("💳 <b>Card Information</b>")
            if is_valid_value(bank):
                lines.append(f"   🏦 <b>Bank:</b> {bank}")
            if is_valid_value(brand):
                lines.append(f"   🏷️ <b>Brand:</b> {brand}")
            if is_valid_value(card_type):
                lines.append(f"   📋 <b>Type:</b> {card_type}")
            if is_valid_value(level):
                lines.append(f"   ⭐ <b>Level:</b> {level}")
            if is_valid_value(base):
                lines.append(f"   🔧 <b>Base:</b> {base}")
            if is_valid_value(bin_number):
                lines.append(f"   🔢 <b>BIN:</b> {bin_number}")
            if is_valid_value(expiry):
                lines.append(f"   📅 <b>Expiry:</b> {expiry}")
            if is_valid_value(code):
                lines.append(f"   🔐 <b>Code:</b> {code}")
            lines.append("")

            # Card data section (sensitive information - display with caution)
            sensitive_fields = [track1, track2, pin]
            if any(is_valid_value(field) for field in sensitive_fields):
                lines.append("🔒 <b>Card Data</b>")
                if is_valid_value(track1):
                    lines.append(f"   📊 <b>Track 1:</b> {track1}")
                if is_valid_value(track2):
                    lines.append(f"   📊 <b>Track 2:</b> {track2}")
                if is_valid_value(pin):
                    lines.append(f"   🔑 <b>PIN:</b> {pin}")
                lines.append("")

            # Financial information
            lines.append("💰 <b>Financial Details</b>")
            if is_valid_value(price):
                try:
                    price_float = float(price)
                    lines.append(f"   💵 <b>Price:</b> ${price_float:.2f}")
                except (ValueError, TypeError):
                    lines.append(f"   💵 <b>Price:</b> {price}")

            # Check if refundable
            refundable = card_data.get("refundable")
            if is_valid_value(refundable):
                refund_text = "Yes" if refundable else "No"
                lines.append(f"   🔄 <b>Refundable:</b> {refund_text}")
            lines.append("")

            # Location information (if available)
            location_fields = [country, state, city, zip_code, address]
            if any(is_valid_value(field) for field in location_fields):
                lines.append("🌍 <b>Location Details</b>")
                if is_valid_value(country):
                    lines.append(f"   🏳️ <b>Country:</b> {country}")
                if is_valid_value(state):
                    lines.append(f"   📍 <b>State:</b> {state}")
                if is_valid_value(city):
                    lines.append(f"   🏙️ <b>City:</b> {city}")
                if is_valid_value(zip_code):
                    lines.append(f"   📮 <b>ZIP:</b> {zip_code}")
                if is_valid_value(address):
                    lines.append(f"   🏠 <b>Address:</b> {address}")
                lines.append("")

            # Cardholder information (filtered)
            cardholder_fields = [name, email, phone]
            if any(is_valid_value(field) for field in cardholder_fields):
                lines.append("👤 <b>Cardholder Information</b>")
                if is_valid_value(name):
                    lines.append(f"   📝 <b>Name:</b> {name}")
                if is_valid_value(email):
                    # Partially mask email for privacy
                    masked_email = self._mask_email(email)
                    lines.append(f"   📧 <b>Email:</b> {masked_email}")
                if is_valid_value(phone):
                    # Partially mask phone for privacy
                    masked_phone = self._mask_phone(phone)
                    lines.append(f"   📞 <b>Phone:</b> {masked_phone}")
                lines.append("")

            # Order timeline
            date_fields = [created_at, start_date, viewed_at, check_date, refund_at]
            if any(is_valid_value(field) for field in date_fields):
                lines.append("📅 <b>Order Timeline</b>")
                if is_valid_value(created_at):
                    lines.append(f"   📝 <b>Created:</b> {self._format_date(created_at)}")
                if is_valid_value(start_date):
                    lines.append(f"   🚀 <b>Started:</b> {self._format_date(start_date)}")
                if is_valid_value(viewed_at):
                    lines.append(f"   👁️ <b>Viewed:</b> {self._format_date(viewed_at)}")
                if is_valid_value(check_date):
                    lines.append(f"   🔍 <b>Checked:</b> {self._format_date(check_date)}")
                if is_valid_value(refund_at):
                    lines.append(f"   🔄 <b>Refunded:</b> {self._format_date(refund_at)}")
                lines.append("")

            # Technical details (collapsed)
            lines.append("🔧 <b>Technical Info</b>")
            if is_valid_value(order_id):
                lines.append(f"   🆔 <b>Order ID:</b> {order_id}")
            if is_valid_value(user_id):
                lines.append(f"   👤 <b>User ID:</b> {user_id}")
            if is_valid_value(product_id):
                lines.append(f"   🎯 <b>Product ID:</b> {product_id}")
            if is_valid_value(seller_id) and seller_id != "Not Allowed":
                lines.append(f"   🏪 <b>Seller ID:</b> {seller_id}")

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Error formatting card details: {e}")
            return "❌ <b>Error loading card details</b>\n\nPlease try again or contact support if the issue persists."

    def _mask_email(self, email: str) -> str:
        """Partially mask email for privacy"""
        try:
            if "@" in email:
                local, domain = email.split("@", 1)
                if len(local) > 3:
                    masked_local = local[:2] + "*" * (len(local) - 3) + local[-1]
                else:
                    masked_local = local[0] + "*" * (len(local) - 1)
                return f"{masked_local}@{domain}"
            return email
        except Exception:
            return email

    def _mask_phone(self, phone: str) -> str:
        """Partially mask phone for privacy"""
        try:
            # Remove non-digit characters for masking
            digits = ''.join(filter(str.isdigit, phone))
            if len(digits) >= 6:
                # Show first 3 and last 2 digits
                masked = digits[:3] + "*" * (len(digits) - 5) + digits[-2:]
                # Preserve original formatting structure
                result = phone
                for i, char in enumerate(phone):
                    if char.isdigit():
                        digit_index = len([c for c in phone[:i] if c.isdigit()])
                        if digit_index < len(masked):
                            result = result[:i] + masked[digit_index] + result[i+1:]
                return result
            return phone
        except Exception:
            return phone

    def _format_date(self, date_str: str) -> str:
        """Format date string for display"""
        try:
            if "T" in date_str:
                # ISO format
                from datetime import datetime
                dt = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
                return dt.strftime("%Y-%m-%d %H:%M UTC")
            return date_str
        except Exception:
            return date_str

    def _get_status_icon(self, status: str) -> str:
        """Get appropriate icon for card status"""
        status_lower = status.lower()
        if status_lower in ["active", "valid", "live", "working", "good"]:
            return "✅"
        elif status_lower in ["pending", "checking", "processing", "verifying"]:
            return "🔄"
        elif status_lower in ["dead", "invalid", "expired", "blocked", "declined"]:
            return "❌"
        elif status_lower in ["unknown", "unclear", "mixed"]:
            return "❓"
        else:
            return "ℹ️"

    def _format_status_message(self, status: str, data: dict) -> str:
        """Format enhanced status message with additional details"""
        status_lower = status.lower()

        # Base message
        if status_lower in ["active", "valid", "live", "working", "good"]:
            base_msg = f"Card Status: {status.upper()}"
        elif status_lower in ["pending", "checking", "processing", "verifying"]:
            base_msg = f"Card Status: {status.upper()}"
        elif status_lower in ["dead", "invalid", "expired", "blocked", "declined"]:
            base_msg = f"Card Status: {status.upper()}"
        else:
            base_msg = f"Card Status: {status}"

        # Add additional details if available
        details = []
        if "response_time" in data:
            details.append(f"Response: {data['response_time']}ms")
        if "last_checked" in data:
            details.append(f"Last checked: {data['last_checked']}")
        if "confidence" in data:
            details.append(f"Confidence: {data['confidence']}%")

        if details:
            return f"{base_msg}\n\n{' • '.join(details)}"
        else:
            return base_msg

    def _format_enhanced_status_message(self, status: str, data: dict) -> str:
        """Format comprehensive status message with detailed information"""
        status_lower = status.lower()

        # Status interpretation
        if status_lower in ["active", "valid", "live", "working", "good", "started"]:
            status_desc = "✅ ACTIVE - Card is working and valid"
            recommendation = "💡 This card appears to be in good condition and ready for use."
        elif status_lower in ["pending", "checking", "processing", "verifying"]:
            status_desc = "🔄 PROCESSING - Card verification in progress"
            recommendation = "⏳ Please wait for verification to complete."
        elif status_lower in ["dead", "invalid", "expired", "blocked", "declined", "refunded"]:
            status_desc = "❌ INACTIVE - Card is not working"
            recommendation = "⚠️ This card may not be usable. Consider requesting a refund if eligible."
        elif status_lower in ["unknown", "unclear", "mixed"]:
            status_desc = "❓ UNKNOWN - Status unclear"
            recommendation = "🔍 Try checking again or contact support for clarification."
        else:
            status_desc = f"ℹ️ {status.upper()}"
            recommendation = "📞 Contact support if you need clarification on this status."

        # Build comprehensive message
        lines = [status_desc]

        # Add technical details if available
        tech_details = []
        if data.get("canCheck") is not None:
            can_check = "Yes" if data["canCheck"] else "No"
            tech_details.append(f"Can Check: {can_check}")

        if data.get("refundable") is not None:
            refundable = "Yes" if data["refundable"] else "No"
            tech_details.append(f"Refundable: {refundable}")

        if data.get("checkedAt"):
            tech_details.append(f"Last Checked: {self._format_date(data['checkedAt'])}")

        if data.get("viewedAt"):
            tech_details.append(f"Last Viewed: {self._format_date(data['viewedAt'])}")

        if tech_details:
            lines.append("")
            lines.append("📊 Technical Details:")
            for detail in tech_details:
                lines.append(f"   • {detail}")

        lines.append("")
        lines.append(recommendation)

        return "\n".join(lines)

    def _format_timer(self, expiry_ts: int) -> str:
        remaining = max(0, expiry_ts - int(datetime.now(timezone.utc).timestamp()))
        return f"⏳ <b>Time left:</b> {remaining}s"

    def _create_progress_bar(self, remaining: int, total: int) -> str:
        """Create a visual progress bar for the countdown timer"""
        if total <= 0:
            return ""

        progress = remaining / total
        bar_length = 10
        filled_length = int(bar_length * progress)

        # Use different characters for different progress levels
        if progress > 0.6:
            fill_char = "🟢"
        elif progress > 0.3:
            fill_char = "🟡"
        else:
            fill_char = "🔴"

        empty_char = "⚪"

        bar = fill_char * filled_length + empty_char * (bar_length - filled_length)
        percentage = int(progress * 100)

        return f"📊 {bar} {percentage}%"

    async def _run_countdown(
        self, chat_id: int, message_id: int, card_id: str, expiry_ts: int, body_text: str, order_id: str
    ) -> None:
        """Edit the message to update the countdown and disable the button at 0."""
        try:
            settings = get_settings()
            from aiogram import Bot

            bot = Bot(token=settings.BOT_TOKEN)
            # Try to update every second
            last_remaining = None
            while True:
                now_ts = int(datetime.now(timezone.utc).timestamp())
                remaining = max(0, expiry_ts - now_ts)
                if remaining == 0:
                    # Disable the button and update timer to 0
                    try:
                        await self._disable_check_button_by_ids(bot, chat_id, message_id, card_id)
                    finally:
                        break
                # Only edit when value changes to reduce calls
                if remaining != last_remaining:
                    try:
                        # Enhanced timer display with progress indicator
                        progress_bar = self._create_progress_bar(remaining, 60)

                        await bot.edit_message_text(
                            chat_id=chat_id,
                            message_id=message_id,
                            text=(
                                f"🔎 <b>Card #{card_id} Details</b>\n"
                                f"{self._format_timer(expiry_ts)}\n"
                                f"{progress_bar}\n\n"
                                f"{body_text}\n"
                                + DEMO_WATERMARK
                            ),
                            reply_markup=InlineKeyboardMarkup(
                                inline_keyboard=[
                                    [
                                        InlineKeyboardButton(
                                            text=f"🔍 Check Card ({remaining}s)",
                                            callback_data=f"orders:check:{order_id}:{card_id}:{expiry_ts}",
                                        )
                                    ],
                                    [
                                        InlineKeyboardButton(
                                            text="📋 Full Details",
                                            callback_data=f"orders:view_card_details:{card_id}",
                                        )
                                    ],
                                    [
                                        InlineKeyboardButton(
                                            text="🛒 View Cart", callback_data="local:cart:view"
                                        ),
                                        InlineKeyboardButton(
                                            text="⬅️ Back", callback_data="menu:history"
                                        ),
                                    ],
                                ]
                            ),
                            parse_mode="HTML",
                        )
                    except Exception:
                        # Ignore edit errors (message may have changed)
                        pass
                    last_remaining = remaining
                await asyncio.sleep(1)
            await bot.session.close()
        except Exception as e:
            logger.error(f"Countdown error: {e}")

    async def _disable_check_button(self, message, card_id: str) -> None:
        try:
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⛔ Check Period Expired", callback_data="noop")],
                    [
                        InlineKeyboardButton(
                            text="📋 View Full Details",
                            callback_data=f"orders:view_card_details:{card_id}",
                        )
                    ],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )
            await message.edit_reply_markup(reply_markup=kb)
        except Exception:
            pass

    async def _disable_check_button_by_ids(
        self, bot, chat_id: int, message_id: int, card_id: str
    ) -> None:
        try:
            kb = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="⛔ Check Period Expired", callback_data="noop")],
                    [
                        InlineKeyboardButton(
                            text="📋 View Full Details",
                            callback_data=f"orders:view_card_details:{card_id}",
                        )
                    ],
                    [
                        InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                        InlineKeyboardButton(text="⬅️ Back", callback_data="menu:history"),
                    ],
                ]
            )
            await bot.edit_message_reply_markup(
                chat_id=chat_id, message_id=message_id, reply_markup=kb
            )
        except Exception:
            pass


def get_orders_router() -> Router:
    router = Router()
    attach_common_middlewares(router)
    handlers = OrdersHandlers()

    router.callback_query.register(
        handlers.cb_view_purchased_card, F.data.startswith("orders:view_card:")
    )
    router.callback_query.register(
        handlers.cb_view_card_details, F.data.startswith("orders:view_card_details:")
    )
    router.callback_query.register(
        handlers.cb_check_card, F.data.startswith("orders:check:")
    )
    router.callback_query.register(
        handlers.cb_orders_menu, F.data == "menu:orders"
    )

    logger.info("Orders handlers registered")
    return router
